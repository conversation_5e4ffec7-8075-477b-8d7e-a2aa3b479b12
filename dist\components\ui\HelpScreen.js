import { jsx as _jsx } from "react/jsx-runtime";
import { useInput } from 'ink';
import { TextBuffer } from './TextBuffer.js';
import { MaxBoxSized } from './MaxBoxSized.js';
export const HelpScreen = ({ themeManager, onClose, }) => {
    useInput((input, key) => {
        if (key.escape || key.return || input === 'q' || input === 'Q') {
            onClose();
        }
    });
    // Convert help content to TextLine format for better display
    const helpLines = [
        { id: '1', content: '🔧 Arien AI CLI Help', color: 'primary', type: 'header' },
        { id: '2', content: '', color: 'muted' },
        { id: '3', content: 'Keyboard Shortcuts:', color: 'secondary', type: 'section' },
        { id: '4', content: '  Enter       - Send message', color: 'muted' },
        { id: '5', content: '  Ctrl+H      - Show this help', color: 'muted' },
        { id: '6', content: '  Ctrl+L      - Clear chat history', color: 'muted' },
        { id: '7', content: '  Ctrl+C      - Exit application', color: 'muted' },
        { id: '8', content: '', color: 'muted' },
        { id: '9', content: 'Features:', color: 'secondary', type: 'section' },
        { id: '10', content: '  • Multi-provider AI support (Google, OpenAI, DeepSeek, Anthropic)', color: 'muted' },
        { id: '11', content: '  • Function calling with built-in tools', color: 'muted' },
        { id: '12', content: '  • Real-time streaming responses', color: 'muted' },
        { id: '13', content: '  • File operations and shell commands', color: 'muted' },
        { id: '14', content: '  • MCP (Model Context Protocol) support', color: 'muted' },
        { id: '15', content: '', color: 'muted' },
        { id: '16', content: 'Available Tools:', color: 'secondary', type: 'section' },
        { id: '17', content: '  • File operations (read, write, edit)', color: 'muted' },
        { id: '18', content: '  • Shell command execution', color: 'muted' },
        { id: '19', content: '  • Web search and fetch', color: 'muted' },
        { id: '20', content: '  • Memory and context management', color: 'muted' },
        { id: '21', content: '  • Code analysis and modification', color: 'muted' },
        { id: '22', content: '', color: 'muted' },
        { id: '23', content: 'Examples:', color: 'secondary', type: 'section' },
        { id: '24', content: '  "Read the package.json file"', color: 'muted' },
        { id: '25', content: '  "Search for React components in src/"', color: 'muted' },
        { id: '26', content: '  "Create a new TypeScript file with a class"', color: 'muted' },
        { id: '27', content: '  "Run npm install and show the output"', color: 'muted' },
        { id: '28', content: '', color: 'muted' },
        { id: '29', content: 'Navigation:', color: 'secondary', type: 'section' },
        { id: '30', content: '  • Use / to search within help content', color: 'info' },
        { id: '31', content: '  • Use ↑↓ to scroll through content', color: 'info' },
        { id: '32', content: '  • Press Enter, Esc, or Q to close help', color: 'warning' },
    ];
    return (_jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: "100%", maxHeight: "100%", responsive: true, padding: 1, children: _jsx(TextBuffer, { themeManager: themeManager, lines: helpLines, maxHeight: 25, scrollable: true, enableSearch: true, showLineNumbers: false, showTimestamps: false, autoScroll: false, title: "\uD83D\uDD27 Arien AI CLI Help", borderStyle: "round", borderColor: "blue", padding: 1, emptyMessage: "No help content available", variant: "detailed", helpText: "Use / to search, \u2191\u2193 to scroll, Enter/Esc/Q to close" }) }));
};
//# sourceMappingURL=HelpScreen.js.map