import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { RadioButtons } from '../ui/RadioButtons.js';
import { MaxBoxSized } from '../ui/MaxBoxSized.js';
export const ConfigurationManager = ({ themeManager, isOpen, onClose, onSave, }) => {
    const [selectedSectionIndex, setSelectedSectionIndex] = useState(0);
    const [selectedSettingIndex, setSelectedSettingIndex] = useState(0);
    const [editingValue, setEditingValue] = useState('');
    const [, setIsEditing] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);
    const [mode, setMode] = useState('sections');
    // Convert sections to RadioOptions
    const sectionOptions = configSections.map(section => ({
        value: section.id,
        label: section.name,
        description: section.description,
    }));
    // Convert current section settings to RadioOptions
    const currentSection = configSections[selectedSectionIndex];
    const settingOptions = currentSection?.settings.map(setting => ({
        value: setting.id,
        label: setting.name,
        description: `${setting.description} (${setting.type})`,
    })) || [];
    const [configSections, setConfigSections] = useState([
        {
            id: 'general',
            name: 'General Settings',
            description: 'Basic application configuration',
            settings: [
                {
                    id: 'theme',
                    name: 'Theme',
                    description: 'Application color theme',
                    type: 'select',
                    value: 'default',
                    defaultValue: 'default',
                    options: ['default', 'dark', 'light', 'neon', 'minimal', 'retro'],
                    required: true,
                },
                {
                    id: 'autoSave',
                    name: 'Auto Save',
                    description: 'Automatically save conversations',
                    type: 'boolean',
                    value: true,
                    defaultValue: true,
                },
                {
                    id: 'maxMessages',
                    name: 'Max Messages',
                    description: 'Maximum messages to keep in memory',
                    type: 'number',
                    value: 100,
                    defaultValue: 100,
                    validation: (value) => {
                        const num = Number(value);
                        if (isNaN(num) || num < 1 || num > 1000) {
                            return 'Must be a number between 1 and 1000';
                        }
                        return null;
                    },
                },
            ],
        },
        {
            id: 'providers',
            name: 'AI Providers',
            description: 'Configure AI service providers',
            settings: [
                {
                    id: 'defaultProvider',
                    name: 'Default Provider',
                    description: 'Default AI provider to use',
                    type: 'select',
                    value: 'openai',
                    defaultValue: 'openai',
                    options: ['openai', 'anthropic', 'google', 'deepseek'],
                    required: true,
                },
                {
                    id: 'openaiApiKey',
                    name: 'OpenAI API Key',
                    description: 'API key for OpenAI services',
                    type: 'password',
                    value: '',
                    defaultValue: '',
                },
                {
                    id: 'anthropicApiKey',
                    name: 'Anthropic API Key',
                    description: 'API key for Anthropic services',
                    type: 'password',
                    value: '',
                    defaultValue: '',
                },
                {
                    id: 'googleApiKey',
                    name: 'Google API Key',
                    description: 'API key for Google AI services',
                    type: 'password',
                    value: '',
                    defaultValue: '',
                },
            ],
        },
        {
            id: 'interface',
            name: 'Interface',
            description: 'User interface preferences',
            settings: [
                {
                    id: 'showTimestamps',
                    name: 'Show Timestamps',
                    description: 'Display message timestamps',
                    type: 'boolean',
                    value: true,
                    defaultValue: true,
                },
                {
                    id: 'messageFormat',
                    name: 'Message Format',
                    description: 'How to display messages',
                    type: 'select',
                    value: 'markdown',
                    defaultValue: 'markdown',
                    options: ['markdown', 'plain', 'code'],
                },
                {
                    id: 'terminalWidth',
                    name: 'Terminal Width',
                    description: 'Maximum width for text display',
                    type: 'number',
                    value: 80,
                    defaultValue: 80,
                    validation: (value) => {
                        const num = Number(value);
                        if (isNaN(num) || num < 40 || num > 200) {
                            return 'Must be a number between 40 and 200';
                        }
                        return null;
                    },
                },
            ],
        },
    ]);
    // Handler for section selection
    const handleSectionSelection = (value, _option) => {
        const sectionIndex = configSections.findIndex(section => section.id === value);
        if (sectionIndex >= 0) {
            setSelectedSectionIndex(sectionIndex);
        }
    };
    const handleSectionSubmit = (value, _option) => {
        const sectionIndex = configSections.findIndex(section => section.id === value);
        if (sectionIndex >= 0) {
            setSelectedSectionIndex(sectionIndex);
            setMode('settings');
            setSelectedSettingIndex(0);
        }
    };
    // Handler for setting selection
    const handleSettingSelection = (value, _option) => {
        const settingIndex = currentSection.settings.findIndex(setting => setting.id === value);
        if (settingIndex >= 0) {
            setSelectedSettingIndex(settingIndex);
        }
    };
    const handleSettingSubmit = (value, _option) => {
        const settingIndex = currentSection.settings.findIndex(setting => setting.id === value);
        if (settingIndex >= 0) {
            setSelectedSettingIndex(settingIndex);
            const setting = currentSection.settings[settingIndex];
            if (setting.type === 'boolean') {
                updateSettingValue(setting.id, !setting.value);
            }
            else if (setting.type === 'select' && setting.options) {
                // For select types, we'll handle them with a separate RadioButtons
                setMode('editing');
                setIsEditing(true);
                setEditingValue(setting.value?.toString() || '');
            }
            else {
                setMode('editing');
                setIsEditing(true);
                setEditingValue(setting.value?.toString() || '');
            }
        }
    };
    useInput((input, key) => {
        if (!isOpen)
            return;
        if (key.escape) {
            if (mode === 'editing') {
                setMode('settings');
                setIsEditing(false);
                setEditingValue('');
                return;
            }
            if (mode === 'settings') {
                setMode('sections');
                return;
            }
            if (hasChanges) {
                // Could show confirmation dialog here
            }
            onClose();
            return;
        }
        // Most navigation is now handled by RadioButtons components
        // Keep only the editing mode and global shortcuts
        if (mode === 'editing') {
            if (key.return) {
                const setting = configSections[selectedSectionIndex].settings[selectedSettingIndex];
                const validation = setting.validation?.(editingValue);
                if (!validation) {
                    let value = editingValue;
                    if (setting.type === 'number') {
                        value = Number(editingValue);
                    }
                    else if (setting.type === 'boolean') {
                        value = editingValue.toLowerCase() === 'true';
                    }
                    updateSettingValue(setting.id, value);
                    setMode('settings');
                    setIsEditing(false);
                    setEditingValue('');
                }
                return;
            }
            if (key.backspace || key.delete) {
                setEditingValue(prev => prev.slice(0, -1));
                return;
            }
            if (input && input.length === 1 && !key.ctrl && !key.meta) {
                setEditingValue(prev => prev + input);
            }
        }
        // Global shortcuts
        if (key.ctrl && input === 's') {
            handleSave();
            return;
        }
    });
    const updateSettingValue = (settingId, value) => {
        setConfigSections(prev => prev.map(section => ({
            ...section,
            settings: section.settings.map(setting => setting.id === settingId ? { ...setting, value } : setting),
        })));
        setHasChanges(true);
    };
    const handleSave = () => {
        const config = {};
        configSections.forEach(section => {
            section.settings.forEach(setting => {
                config[setting.id] = setting.value;
            });
        });
        onSave?.(config);
        setHasChanges(false);
    };
    if (!isOpen)
        return null;
    const renderSectionsList = () => (_jsx(RadioButtons, { themeManager: themeManager, options: sectionOptions, selectedValue: configSections[selectedSectionIndex]?.id, onSelectionChange: handleSectionSelection, onSubmit: handleSectionSubmit, title: "Configuration Sections:", showDescriptions: true, variant: "detailed", helpText: "Use \u2191\u2193 to navigate, Enter to select", maxHeight: 15 }));
    const renderSettingsList = () => {
        const enhancedSettingOptions = settingOptions.map(option => {
            const setting = currentSection.settings.find(s => s.id === option.value);
            const valueDisplay = setting?.type === 'password'
                ? '*'.repeat(setting.value?.length || 0)
                : setting?.value?.toString() || 'Not set';
            return {
                ...option,
                description: `${option.description} | Current: ${valueDisplay}`,
            };
        });
        return (_jsx(Box, { flexDirection: "column", children: _jsx(RadioButtons, { themeManager: themeManager, options: enhancedSettingOptions, selectedValue: currentSection.settings[selectedSettingIndex]?.id, onSelectionChange: handleSettingSelection, onSubmit: handleSettingSubmit, title: `${currentSection.name} Settings:`, showDescriptions: true, variant: "detailed", helpText: "Use \u2191\u2193 to navigate, Enter to edit, Esc to go back", maxHeight: 15 }) }));
    };
    const renderEditingInterface = () => {
        const setting = configSections[selectedSectionIndex].settings[selectedSettingIndex];
        const validation = setting.validation?.(editingValue);
        // If it's a select type with options, use RadioButtons
        if (setting.type === 'select' && setting.options) {
            const selectOptions = setting.options.map(option => ({
                value: option,
                label: option,
                description: `Select ${option}`,
            }));
            const handleSelectChange = (value, _option) => {
                setEditingValue(value);
            };
            const handleSelectSubmit = (value, _option) => {
                updateSettingValue(setting.id, value);
                setMode('settings');
                setIsEditing(false);
                setEditingValue('');
            };
            return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`Editing: ${setting.name}`) }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Description: ') }), _jsx(Text, { children: themeManager.muted(setting.description) })] }), _jsx(Text, {}), _jsx(RadioButtons, { themeManager: themeManager, options: selectOptions, selectedValue: editingValue, onSelectionChange: handleSelectChange, onSubmit: handleSelectSubmit, title: "Select an option:", showDescriptions: false, variant: "compact", helpText: "Use \u2191\u2193 to navigate, Enter to select, Esc to cancel", maxHeight: 10 })] }));
        }
        // For other types, use the original text input interface
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`Editing: ${setting.name}`) }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Description: ') }), _jsx(Text, { children: themeManager.muted(setting.description) })] }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Current Value: ') }), _jsx(Text, { children: themeManager.primary(editingValue || '...') })] }), setting.options && (_jsxs(Box, { marginLeft: 2, marginTop: 1, children: [_jsx(Text, { children: themeManager.secondary('Options: ') }), _jsx(Text, { children: themeManager.muted(setting.options.join(', ')) })] })), validation && (_jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { children: themeManager.error(`Error: ${validation}`) }) }))] }));
    };
    const renderControls = () => (_jsx(Box, { marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.muted('Controls:') }), mode === 'sections' && (_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Select • Esc Close') })), mode === 'settings' && (_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Edit • Esc Back • Ctrl+S Save') })), mode === 'editing' && (_jsx(Text, { children: themeManager.muted('  Type value • Enter Save • Esc Cancel') })), hasChanges && (_jsx(Text, { children: themeManager.warning('  Unsaved changes!') }))] }) }));
    return (_jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: "100%", maxHeight: "100%", responsive: true, padding: 1, children: _jsx(Box, { flexDirection: "column", children: _jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('⚙️  Configuration Manager') }), _jsx(Text, {}), mode === 'sections' && renderSectionsList(), mode === 'settings' && renderSettingsList(), mode === 'editing' && renderEditingInterface(), renderControls()] }) }) }) }));
};
//# sourceMappingURL=ConfigurationManager.js.map